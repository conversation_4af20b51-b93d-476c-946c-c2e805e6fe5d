; AutoHotkey script for double-tap arrow key functionality
; Double-tap left or right arrow to hold that direction until any other key is pressed

#NoEnv
#SingleInstance Force
#Persistent

; Variables to track double-tap timing and state
DoubleTapDelay := 300  ; Maximum time between taps (in milliseconds)
LastLeftTap := 0
LastRightTap := 0
HoldingLeft := false
HoldingRight := false

; Function to stop holding any direction
StopHolding() {
    global HoldingLeft, HoldingRight
    if (HoldingLeft) {
        Send, {Left up}
        HoldingLeft := false
    }
    if (HoldingRight) {
        Send, {Right up}
        HoldingRight := false
    }
}

; Left arrow key handler
Left::
    CurrentTime := A_TickCount
    TimeSinceLastTap := CurrentTime - LastLeftTap
    
    ; Stop any current holding
    StopHolding()
    
    ; Check if this is a double-tap
    if (TimeSinceLastTap <= DoubleTapDelay && TimeSinceLastTap > 0) {
        ; Double-tap detected - start holding left
        HoldingLeft := true
        Send, {Left down}
        LastLeftTap := 0  ; Reset to prevent triple-tap
    } else {
        ; Single tap - send normal left arrow
        Send, {Left}
        LastLeftTap := CurrentTime
    }
return

; Right arrow key handler
Right::
    CurrentTime := A_TickCount
    TimeSinceLastTap := CurrentTime - LastRightTap
    
    ; Stop any current holding
    StopHolding()
    
    ; Check if this is a double-tap
    if (TimeSinceLastTap <= DoubleTapDelay && TimeSinceLastTap > 0) {
        ; Double-tap detected - start holding right
        HoldingRight := true
        Send, {Right down}
        LastRightTap := 0  ; Reset to prevent triple-tap
    } else {
        ; Single tap - send normal right arrow
        Send, {Right}
        LastRightTap := CurrentTime
    }
return

; Hook for any other key press to stop holding
~*a::
~*b::
~*c::
~*d::
~*e::
~*f::
~*g::
~*h::
~*i::
~*j::
~*k::
~*l::
~*m::
~*n::
~*o::
~*p::
~*q::
~*r::
~*s::
~*t::
~*u::
~*v::
~*w::
~*x::
~*y::
~*z::
~*0::
~*1::
~*2::
~*3::
~*4::
~*5::
~*6::
~*7::
~*8::
~*9::
~*Space::
~*Enter::
~*Escape::
~*Tab::
~*Backspace::
~*Delete::
~*Home::
~*End::
~*PgUp::
~*PgDn::
~*Up::
~*Down::
~*Insert::
~*F1::
~*F2::
~*F3::
~*F4::
~*F5::
~*F6::
~*F7::
~*F8::
~*F9::
~*F10::
~*F11::
~*F12::
~*Shift::
~*Ctrl::
~*Alt::
~*LWin::
~*RWin::
~*AppsKey::
~*PrintScreen::
~*ScrollLock::
~*Pause::
~*NumLock::
~*CapsLock::
~*LButton::
~*RButton::
~*MButton::
~*WheelUp::
~*WheelDown::
    StopHolding()
return

; Exit hotkey (Ctrl+Alt+Q)
^!q::ExitApp

`::
Suspend
ToolTip % (A_IsSuspended) ? "" : "qt", 1240, 1250
Pause,,1
return